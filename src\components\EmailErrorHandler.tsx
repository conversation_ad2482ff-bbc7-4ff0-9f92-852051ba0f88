import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  Mail, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Info,
  Clock,
  Shield,
  HelpCircle
} from 'lucide-react';
import { EmailDiagnostics } from '@/services/passwordResetService';

interface EmailErrorHandlerProps {
  error?: string;
  diagnostics?: EmailDiagnostics;
  email: string;
  onRetry?: () => void;
  onShowTroubleshooting?: () => void;
  className?: string;
}

export const EmailErrorHandler: React.FC<EmailErrorHandlerProps> = ({
  error,
  diagnostics,
  email,
  onRetry,
  onShowTroubleshooting,
  className = ''
}) => {
  const getErrorIcon = (errorCode?: string) => {
    switch (errorCode) {
      case 'auth/user-not-found':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'auth/invalid-email':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'auth/too-many-requests':
        return <Clock className="h-4 w-4 text-orange-500" />;
      case 'auth/network-request-failed':
        return <Shield className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
    }
  };

  const getErrorTitle = (errorCode?: string) => {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'Account Not Found';
      case 'auth/invalid-email':
        return 'Invalid Email Format';
      case 'auth/email-already-in-use':
        return 'Account Verification Issue';
      case 'auth/too-many-requests':
        return 'Too Many Requests';
      case 'auth/network-request-failed':
        return 'Network Error';
      case 'auth/quota-exceeded':
        return 'Service Temporarily Unavailable';
      default:
        return 'Email Delivery Issue';
    }
  };

  const getErrorMessage = (errorCode?: string) => {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'No account exists with this email address. Please check the email or sign up for a new account.';
      case 'auth/invalid-email':
        return 'The email address format is invalid. Please check for typos and try again.';
      case 'auth/email-already-in-use':
        return 'Your account exists but there was an issue with email verification. The password reset email should still be sent.';
      case 'auth/too-many-requests':
        return 'Too many password reset requests. Please wait a few minutes before trying again.';
      case 'auth/network-request-failed':
        return 'Unable to connect to our servers. Please check your internet connection and try again.';
      case 'auth/quota-exceeded':
        return 'Our email service is temporarily at capacity. Please try again in a few minutes.';
      default:
        return 'We encountered an issue sending the password reset email. Please try again.';
    }
  };

  const getActionButtons = (errorCode?: string) => {
    const buttons = [];

    // Always show retry button unless it's a user-not-found error
    if (errorCode !== 'auth/user-not-found' && onRetry) {
      buttons.push(
        <Button
          key="retry"
          onClick={onRetry}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
      );
    }

    // Show troubleshooting for delivery issues
    if (onShowTroubleshooting && (
      errorCode === 'auth/network-request-failed' || 
      errorCode === 'auth/quota-exceeded' ||
      !errorCode
    )) {
      buttons.push(
        <Button
          key="troubleshoot"
          onClick={onShowTroubleshooting}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <HelpCircle className="h-4 w-4" />
          Troubleshoot
        </Button>
      );
    }

    return buttons;
  };

  const getSuggestions = (errorCode?: string) => {
    const suggestions = [];

    switch (errorCode) {
      case 'auth/user-not-found':
        suggestions.push('Double-check the email address for typos');
        suggestions.push('Try using a different email address');
        suggestions.push('Sign up for a new account if you don\'t have one');
        break;
      case 'auth/invalid-email':
        suggestions.push('Check for missing @ symbol or domain');
        suggestions.push('Remove any extra spaces');
        suggestions.push('Ensure the domain has a valid extension (.com, .org, etc.)');
        break;
      case 'auth/email-already-in-use':
        suggestions.push('Check your email inbox and spam folder for the reset link');
        suggestions.push('Wait a few minutes for the email to arrive');
        suggestions.push('Try the password reset process again if no email received');
        suggestions.push('Contact support if you continue having issues');
        break;
      case 'auth/too-many-requests':
        suggestions.push('Wait 5-10 minutes before trying again');
        suggestions.push('Check your spam folder for previous emails');
        suggestions.push('Contact support if the issue persists');
        break;
      case 'auth/network-request-failed':
        suggestions.push('Check your internet connection');
        suggestions.push('Try using a different network');
        suggestions.push('Disable VPN if you\'re using one');
        break;
      default:
        suggestions.push('Check your spam/junk folder');
        suggestions.push('Try using a different email provider');
        suggestions.push('Contact support if the problem continues');
    }

    return suggestions;
  };

  if (!error && !diagnostics?.suggestions.length) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <div className="flex items-start gap-3">
            {getErrorIcon(error)}
            <div className="flex-1 space-y-2">
              <div>
                <h4 className="font-medium text-red-800">
                  {getErrorTitle(error)}
                </h4>
                <AlertDescription className="text-red-700">
                  {getErrorMessage(error)}
                </AlertDescription>
              </div>

              {/* Email Information */}
              {diagnostics && (
                <div className="bg-white/50 rounded p-3 space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4" />
                    <span className="font-medium">Email:</span>
                    <code className="bg-white px-2 py-1 rounded text-xs">{email}</code>
                    {diagnostics.provider !== 'Other' && (
                      <Badge variant="secondary" className="text-xs">
                        {diagnostics.provider}
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Suggestions */}
              <div className="space-y-2">
                <h5 className="font-medium text-red-800 text-sm">What you can do:</h5>
                <ul className="space-y-1">
                  {getSuggestions(error).map((suggestion, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-red-700">
                      <CheckCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                {getActionButtons(error)}
              </div>
            </div>
          </div>
        </Alert>
      )}

      {/* Diagnostic Suggestions (for non-error cases) */}
      {!error && diagnostics?.suggestions.length > 0 && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <Info className="h-4 w-4 text-yellow-600" />
          <div className="space-y-2">
            <h4 className="font-medium text-yellow-800">
              Email Suggestions
            </h4>
            <ul className="space-y-1">
              {diagnostics.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-yellow-700">
                  <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        </Alert>
      )}

      {/* Provider-Specific Tips */}
      {diagnostics?.provider && diagnostics.provider !== 'Other' && (
        <Alert className="border-blue-200 bg-blue-50">
          <Info className="h-4 w-4 text-blue-600" />
          <div className="space-y-2">
            <h4 className="font-medium text-blue-800">
              {diagnostics.provider} Email Tips
            </h4>
            <AlertDescription className="text-blue-700">
              {diagnostics.provider === 'Gmail' && (
                <>
                  Gmail users: Check your "Promotions" tab and spam folder. 
                  Consider adding our sender to your contacts for better delivery.
                </>
              )}
              {diagnostics.provider === 'Yahoo' && (
                <>
                  Yahoo users: Check your spam folder and "Bulk" mail. 
                  Yahoo has strict spam filters that may block automated emails.
                </>
              )}
              {diagnostics.provider === 'Outlook' && (
                <>
                  Outlook users: Check your junk email folder and "Clutter" inbox. 
                  Add our sender to your safe senders list.
                </>
              )}
            </AlertDescription>
          </div>
        </Alert>
      )}
    </div>
  );
};
