// Enhanced Password Reset Service with Email Functionality
import {
  sendPasswordResetEmail,
  confirmPasswordReset,
  verifyPasswordResetCode,
  checkActionCode,
  applyActionCode,
  fetchSignInMethodsForEmail
} from 'firebase/auth';
import { auth } from '@/lib/firebase';

export interface PasswordResetResult {
  success: boolean;
  message: string;
  error?: string;
  diagnostics?: EmailDiagnostics;
}

export interface PasswordResetEmailOptions {
  url?: string;
  handleCodeInApp?: boolean;
}

export interface EmailDiagnostics {
  emailExists: boolean;
  emailFormat: string;
  domain: string;
  provider: string;
  suggestions: string[];
  troubleshooting: string[];
}

/**
 * Common domain typos and their corrections
 */
const DOMAIN_TYPOS: { [key: string]: string } = {
  'gamil.com': 'gmail.com',
  'gmai.com': 'gmail.com',
  'gmail.co': 'gmail.com',
  'gmial.com': 'gmail.com',
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'yahoo.co': 'yahoo.com',
  'outlok.com': 'outlook.com',
  'outlook.co': 'outlook.com',
  'hotmial.com': 'hotmail.com',
  'hotmai.com': 'hotmail.com',
  'hotmail.co': 'hotmail.com'
};

/**
 * Validate email format and extract domain information
 */
const validateEmailFormat = (email: string): { isValid: boolean; domain: string; provider: string; suggestion?: string } => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(email);

  if (!isValid) {
    return { isValid: false, domain: '', provider: 'unknown' };
  }

  const [localPart, domain] = email.split('@');
  const lowerDomain = domain.toLowerCase();

  // Check for common typos
  let suggestion: string | undefined;
  if (DOMAIN_TYPOS[lowerDomain]) {
    suggestion = `${localPart}@${DOMAIN_TYPOS[lowerDomain]}`;
  }

  // Common email providers
  const providers: { [key: string]: string } = {
    'gmail.com': 'Gmail',
    'yahoo.com': 'Yahoo',
    'outlook.com': 'Outlook',
    'hotmail.com': 'Hotmail',
    'icloud.com': 'iCloud',
    'protonmail.com': 'ProtonMail',
    'aol.com': 'AOL',
    'live.com': 'Microsoft Live'
  };

  const provider = providers[lowerDomain] || 'Other';

  return { isValid, domain: lowerDomain, provider, suggestion };
};

/**
 * Generate email diagnostics and troubleshooting suggestions
 */
const generateEmailDiagnostics = async (email: string): Promise<EmailDiagnostics> => {
  const { isValid, domain, provider, suggestion } = validateEmailFormat(email);

  let emailExists = true; // Default to true to avoid blocking password reset attempts
  try {
    const signInMethods = await fetchSignInMethodsForEmail(auth, email);
    emailExists = signInMethods.length > 0;
    console.log(`Email existence check for ${email}:`, { signInMethods, emailExists });
  } catch (error: any) {
    console.warn('Error checking email existence for', email, ':', error);

    // Handle specific error cases
    if (error.code === 'auth/invalid-email') {
      emailExists = false; // Invalid email format means it doesn't exist
    } else if (error.code === 'auth/email-already-in-use') {
      emailExists = true; // This error means the email definitely exists
    } else {
      // For other errors (network, etc.), assume it exists to avoid false negatives
      // This is safer - let Firebase's sendPasswordResetEmail handle the actual existence check
      emailExists = true;
      console.log('Defaulting emailExists to true for email:', email);
    }
  }

  const suggestions: string[] = [];
  const troubleshooting: string[] = [];

  // Add suggestions based on email provider
  if (provider === 'Gmail') {
    troubleshooting.push('Check your Gmail spam/junk folder');
    troubleshooting.push('Add <EMAIL> to your contacts');
    troubleshooting.push('Check Gmail\'s "Promotions" or "Updates" tabs');
  } else if (provider === 'Yahoo') {
    troubleshooting.push('Check your Yahoo spam folder');
    troubleshooting.push('Add the sender to your Yahoo contacts');
    troubleshooting.push('Check if Yahoo is blocking automated emails');
  } else if (provider === 'Outlook' || provider === 'Hotmail') {
    troubleshooting.push('Check your Outlook junk email folder');
    troubleshooting.push('Add the sender to your safe senders list');
    troubleshooting.push('Check Outlook\'s focused inbox settings');
  } else {
    troubleshooting.push('Check your spam/junk folder');
    troubleshooting.push('Add the sender to your contacts or safe senders list');
    troubleshooting.push('Contact your email provider if emails are being blocked');
  }

  // Common troubleshooting steps
  troubleshooting.push('Wait 5-10 minutes for email delivery');
  troubleshooting.push('Try requesting the reset email again');
  troubleshooting.push('Check if your email inbox is full');

  // Email format suggestions
  if (!isValid) {
    suggestions.push('Please check your email format (example: <EMAIL>)');
  }

  // Domain typo suggestions
  if (suggestion) {
    suggestions.push(`Did you mean: ${suggestion}?`);
  }

  if (!emailExists) {
    suggestions.push('This email address is not registered. Please sign up first.');
  }

  return {
    emailExists,
    emailFormat: isValid ? 'valid' : 'invalid',
    domain,
    provider,
    suggestions,
    troubleshooting
  };
};

/**
 * Send password reset email to user with enhanced diagnostics
 */
export const sendPasswordResetEmailService = async (
  email: string,
  options?: PasswordResetEmailOptions
): Promise<PasswordResetResult> => {
  try {
    // Generate diagnostics first (but don't block on email existence check)
    const diagnostics = await generateEmailDiagnostics(email);

    // Check if email format is valid
    if (diagnostics.emailFormat === 'invalid') {
      return {
        success: false,
        message: 'Invalid email address format',
        error: 'auth/invalid-email',
        diagnostics
      };
    }

    // Try to send password reset email directly - let Firebase handle existence check
    const actionCodeSettings = {
      url: options?.url || `${window.location.origin}/signin`,
      handleCodeInApp: options?.handleCodeInApp || false,
    };

    console.log('Attempting to send password reset email for:', email);
    await sendPasswordResetEmail(auth, email, actionCodeSettings);

    return {
      success: true,
      message: 'Password reset email sent successfully',
      diagnostics
    };
  } catch (error: any) {
    console.error('Password reset email error:', error);

    // Generate diagnostics for error cases
    const diagnostics = await generateEmailDiagnostics(email);

    let errorMessage = 'Failed to send password reset email';

    switch (error.code) {
      case 'auth/user-not-found':
        errorMessage = 'No account found with this email address';
        break;
      case 'auth/invalid-email':
        errorMessage = 'Invalid email address format';
        break;
      case 'auth/email-already-in-use':
        // This error can occur in some Firebase configurations
        // It actually means the email exists, so we should try sending the reset email
        console.log('Handling auth/email-already-in-use error for email:', email);
        const retryResult = await handleEmailAlreadyInUseError(email, options);
        return {
          ...retryResult,
          diagnostics
        };
      case 'auth/too-many-requests':
        errorMessage = 'Too many requests. Please try again in a few minutes';
        break;
      case 'auth/network-request-failed':
        errorMessage = 'Network error. Please check your internet connection';
        break;
      case 'auth/internal-error':
        errorMessage = 'Internal server error. Please try again later';
        break;
      case 'auth/quota-exceeded':
        errorMessage = 'Email quota exceeded. Please try again later';
        break;
      default:
        errorMessage = error.message || 'Failed to send password reset email';
    }

    return {
      success: false,
      message: errorMessage,
      error: error.code,
      diagnostics
    };
  }
};

/**
 * Direct password reset without pre-checking email existence
 * This bypasses the email existence check and lets Firebase handle it
 */
export const sendPasswordResetEmailDirect = async (
  email: string,
  options?: PasswordResetEmailOptions
): Promise<PasswordResetResult> => {
  try {
    console.log('Sending password reset email directly for:', email);

    // Basic email format validation only
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        success: false,
        message: 'Invalid email address format',
        error: 'auth/invalid-email'
      };
    }

    const actionCodeSettings = {
      url: options?.url || `${window.location.origin}/signin`,
      handleCodeInApp: options?.handleCodeInApp || false,
    };

    await sendPasswordResetEmail(auth, email, actionCodeSettings);

    return {
      success: true,
      message: 'Password reset email sent successfully'
    };
  } catch (error: any) {
    console.error('Direct password reset error:', error);

    let errorMessage = 'Failed to send password reset email';

    switch (error.code) {
      case 'auth/user-not-found':
        errorMessage = 'No account found with this email address. Please check the email or sign up for a new account.';
        break;
      case 'auth/invalid-email':
        errorMessage = 'Invalid email address format';
        break;
      case 'auth/too-many-requests':
        errorMessage = 'Too many requests. Please try again in a few minutes';
        break;
      case 'auth/network-request-failed':
        errorMessage = 'Network error. Please check your internet connection';
        break;
      default:
        errorMessage = error.message || 'Failed to send password reset email';
    }

    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Handle the specific case where Firebase returns auth/email-already-in-use
 * This can happen when the account exists but there are verification issues
 */
export const handleEmailAlreadyInUseError = async (
  email: string,
  options?: PasswordResetEmailOptions
): Promise<PasswordResetResult> => {
  try {
    console.log('Handling email-already-in-use error for:', email);

    // Try to send password reset email directly
    const actionCodeSettings = {
      url: options?.url || `${window.location.origin}/signin`,
      handleCodeInApp: options?.handleCodeInApp || false,
    };

    await sendPasswordResetEmail(auth, email, actionCodeSettings);

    return {
      success: true,
      message: 'Password reset email sent successfully. Your account exists and the reset link has been sent.',
    };
  } catch (error: any) {
    console.error('Error in handleEmailAlreadyInUseError:', error);

    // If it still fails, provide helpful guidance
    return {
      success: false,
      message: 'Account exists but unable to send reset email. Please try again or contact support.',
      error: error.code || 'email-send-failed'
    };
  }
};

/**
 * Send password reset email with retry mechanism
 */
export const sendPasswordResetEmailWithRetry = async (
  email: string,
  options?: PasswordResetEmailOptions,
  maxRetries: number = 3
): Promise<PasswordResetResult> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await sendPasswordResetEmailService(email, options);

      if (result.success) {
        return {
          ...result,
          message: attempt > 1
            ? `Password reset email sent successfully (attempt ${attempt})`
            : result.message
        };
      }

      // If it's a user error (not found, invalid email), don't retry
      if (result.error === 'auth/user-not-found' || result.error === 'auth/invalid-email') {
        return result;
      }

      lastError = result;

      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      lastError = error;

      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  return {
    success: false,
    message: `Failed to send password reset email after ${maxRetries} attempts`,
    error: lastError?.error || 'retry-failed',
    diagnostics: lastError?.diagnostics
  };
};

/**
 * Check email delivery status and provide troubleshooting
 */
export const checkEmailDeliveryStatus = async (email: string): Promise<{
  status: 'delivered' | 'pending' | 'failed' | 'unknown';
  message: string;
  troubleshooting: string[];
}> => {
  const diagnostics = await generateEmailDiagnostics(email);

  // We can't actually check delivery status with Firebase Auth,
  // but we can provide helpful troubleshooting based on the email provider
  return {
    status: 'unknown',
    message: 'Email delivery status cannot be determined. Please check the troubleshooting steps below.',
    troubleshooting: diagnostics.troubleshooting
  };
};

/**
 * Verify password reset code
 */
export const verifyPasswordResetCodeService = async (
  code: string
): Promise<PasswordResetResult & { email?: string }> => {
  try {
    const email = await verifyPasswordResetCode(auth, code);
    
    return {
      success: true,
      message: 'Password reset code is valid',
      email
    };
  } catch (error: any) {
    console.error('Password reset code verification error:', error);
    
    let errorMessage = 'Invalid or expired reset code';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Password reset link has expired. Please request a new one';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid password reset link. Please request a new one';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this reset request';
        break;
      default:
        errorMessage = error.message || 'Invalid or expired reset code';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Confirm password reset with new password
 */
export const confirmPasswordResetService = async (
  code: string,
  newPassword: string
): Promise<PasswordResetResult> => {
  try {
    await confirmPasswordReset(auth, code, newPassword);
    
    return {
      success: true,
      message: 'Password reset successfully'
    };
  } catch (error: any) {
    console.error('Password reset confirmation error:', error);
    
    let errorMessage = 'Failed to reset password';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Password reset link has expired. Please request a new one';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid password reset link. Please request a new one';
        break;
      case 'auth/weak-password':
        errorMessage = 'Password is too weak. Please choose a stronger password';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this reset request';
        break;
      default:
        errorMessage = error.message || 'Failed to reset password';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Check action code (for email verification, password reset, etc.)
 */
export const checkActionCodeService = async (
  code: string
): Promise<PasswordResetResult & { 
  operation?: string;
  email?: string;
  previousEmail?: string;
}> => {
  try {
    const info = await checkActionCode(auth, code);
    
    return {
      success: true,
      message: 'Action code is valid',
      operation: info.operation,
      email: info.data.email || undefined,
      previousEmail: info.data.previousEmail || undefined
    };
  } catch (error: any) {
    console.error('Action code check error:', error);
    
    let errorMessage = 'Invalid or expired action code';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Action code has expired';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid action code';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this action';
        break;
      default:
        errorMessage = error.message || 'Invalid or expired action code';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Apply action code (complete email verification, password reset, etc.)
 */
export const applyActionCodeService = async (
  code: string
): Promise<PasswordResetResult> => {
  try {
    await applyActionCode(auth, code);
    
    return {
      success: true,
      message: 'Action completed successfully'
    };
  } catch (error: any) {
    console.error('Action code apply error:', error);
    
    let errorMessage = 'Failed to complete action';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Action code has expired';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid action code';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this action';
        break;
      default:
        errorMessage = error.message || 'Failed to complete action';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Validate password strength
 */
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one uppercase letter');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one lowercase letter');
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one number');
  }

  // Special character check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one special character');
  }

  return {
    isValid: score >= 3,
    score,
    feedback
  };
};

/**
 * Generate secure password suggestions
 */
export const generatePasswordSuggestions = (): string[] => {
  const adjectives = ['Secure', 'Strong', 'Safe', 'Protected', 'Guarded'];
  const nouns = ['Hotel', 'Stay', 'Room', 'Guest', 'Booking'];
  const numbers = Math.floor(Math.random() * 9999) + 1000;
  const symbols = ['!', '@', '#', '$', '%'];
  
  const suggestions = [];
  
  for (let i = 0; i < 3; i++) {
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    const num = Math.floor(Math.random() * 99) + 10;
    
    suggestions.push(`${adj}${noun}${num}${symbol}`);
  }
  
  return suggestions;
};
