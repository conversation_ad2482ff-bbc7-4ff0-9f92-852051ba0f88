// Booking history component that fetches real data from Firebase
import * as React from 'react';
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import '@/styles/booking-dialogs.css';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  MapPin,
  Users,
  IndianRupee,
  Phone,
  Mail,
  FileText,
  User,
  Loader2,
  AlertCircle,
  RefreshCw,
  Trash2
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { getUserBookings, createSampleBookings, deleteBooking, removeAllUserBookings } from '@/services/bookingService';
import { Booking, BookingStatus } from '@/types/booking';
import { formatCurrency, formatDate } from '@/lib/utils';

interface StaticBookingHistoryProps {
  className?: string;
}

const StaticBookingHistory: React.FC<StaticBookingHistoryProps> = ({ className }) => {
  const { user, firebaseUser } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Test Firestore connection
  const testFirestoreConnection = async () => {
    try {
      console.log('Testing Firestore connection...');
      const { collection, getDocs } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');

      // Try to read from a simple collection
      const testQuery = collection(db, 'bookings');
      const snapshot = await getDocs(testQuery);
      console.log('Firestore connection test successful. Documents found:', snapshot.size);
      return true;
    } catch (error) {
      console.error('Firestore connection test failed:', error);
      return false;
    }
  };

  // Fetch real bookings from Firestore
  const fetchBookings = async () => {
    if (!firebaseUser) {
      setError('Please sign in to view your bookings');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('=== BOOKING FETCH DEBUG ===');
      console.log('User UID:', firebaseUser.uid);
      console.log('User email:', firebaseUser.email);
      console.log('User display name:', firebaseUser.displayName);

      // Test Firestore connection first
      const connectionOk = await testFirestoreConnection();
      if (!connectionOk) {
        throw new Error('Cannot connect to Firestore database');
      }

      console.log('Calling getUserBookings...');
      const result = await getUserBookings(firebaseUser.uid, 20);

      console.log('getUserBookings result:', result);
      console.log('Number of bookings found:', result.bookings.length);

      setBookings(result.bookings);

      if (result.bookings.length === 0) {
        console.log('No bookings found for user');
        setError('No bookings found. Try making a booking first!');
      } else {
        console.log('Successfully loaded', result.bookings.length, 'bookings');
      }
    } catch (error: any) {
      console.error('=== BOOKING FETCH ERROR ===');
      console.error('Error details:', error);
      console.error('Error message:', error.message);
      console.error('Error code:', error.code);

      let errorMessage = 'Failed to load bookings';
      if (error.message) {
        errorMessage += ': ' + error.message;
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      console.log('=== BOOKING FETCH COMPLETE ===');
    }
  };

  useEffect(() => {
    fetchBookings();
  }, [firebaseUser]);

  // Create a test booking for debugging
  const createTestBooking = async () => {
    if (!firebaseUser) {
      toast.error('Please sign in first');
      return;
    }

    try {
      console.log('=== CREATING TEST BOOKING ===');
      console.log('User:', firebaseUser.uid, firebaseUser.email);

      // Import Firebase functions
      const { collection, addDoc, Timestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');

      const testBooking = {
        // Hotel information
        hotelId: 'test-hotel-' + Date.now(),
        hotelName: 'Arunachala Test Hotel',

        // Guest information
        guestName: firebaseUser.displayName || 'Test User',
        guestEmail: firebaseUser.email || '<EMAIL>',
        guestPhone: '+91 9876543210',

        // Booking dates
        checkIn: Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Tomorrow
        checkOut: Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)), // 3 days from now

        // Room details
        rooms: [{
          roomId: 'test-room-1',
          roomType: 'Deluxe Room',
          quantity: 1,
          pricePerNight: 2500,
          amenities: ['WiFi', 'AC', 'TV'],
          maxOccupancy: 2
        }],

        // Guest details
        adults: 2,
        children: 0,
        nights: 2,
        totalRooms: 1,
        totalAmount: 5000,

        // Booking metadata
        bookingSource: 'website',
        userId: firebaseUser.uid,
        createdBy: firebaseUser.uid,
        guestNotes: 'Test booking created for debugging - ' + new Date().toLocaleString(),

        // Timestamps
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),

        // Status
        status: 'confirmed',
        paymentStatus: 'pending'
      };

      console.log('Test booking data:', testBooking);

      // Add document directly to Firestore
      const docRef = await addDoc(collection(db, 'bookings'), testBooking);
      console.log('Test booking created with ID:', docRef.id);

      toast.success(`Test booking created successfully! ID: ${docRef.id}`);

      // Refresh bookings to show the new one
      setTimeout(() => {
        console.log('Refreshing bookings after test booking creation...');
        fetchBookings();
      }, 1500);

    } catch (error: any) {
      console.error('=== TEST BOOKING CREATION ERROR ===');
      console.error('Error creating test booking:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);

      toast.error('Failed to create test booking: ' + error.message);
    }
  };

  // Create sample bookings using the service
  const createSampleBookingsHandler = async () => {
    if (!firebaseUser) {
      toast.error('🔐 Please sign in first to create sample bookings');
      return;
    }

    try {
      console.log('Creating sample bookings...');

      // Show loading toast
      toast.loading('📝 Creating sample bookings...', { id: 'create-samples' });

      await createSampleBookings(firebaseUser.uid, firebaseUser.email || '<EMAIL>');

      toast.success(
        '🎉 Sample bookings created successfully!\n2 demo bookings have been added to your account.',
        {
          id: 'create-samples',
          duration: 4000,
        }
      );

      // Refresh bookings to show the new ones
      setTimeout(() => {
        console.log('Refreshing bookings after sample creation...');
        fetchBookings();
      }, 1500);
    } catch (error: any) {
      console.error('Error creating sample bookings:', error);
      toast.error(
        `❌ Failed to create sample bookings\n${error.message || 'Please try again or check your connection.'}`,
        {
          id: 'create-samples',
          duration: 5000,
        }
      );
    }
  };

  // Delete a specific booking
  const handleDeleteBooking = async (bookingId: string, hotelName: string) => {
    try {
      console.log('Deleting booking:', bookingId);

      // Show loading toast
      toast.loading('Deleting booking...', { id: 'delete-booking' });

      await deleteBooking(bookingId);

      // Show success toast
      toast.success(
        `✅ Booking deleted successfully!\n${hotelName} has been removed from your bookings.`,
        {
          id: 'delete-booking',
          duration: 4000,
        }
      );

      // Refresh bookings to show updated list
      setTimeout(() => {
        console.log('Refreshing bookings after deletion...');
        fetchBookings();
      }, 1000);
    } catch (error: any) {
      console.error('Error deleting booking:', error);
      toast.error(
        `❌ Failed to delete booking\n${error.message || 'Please try again or contact support.'}`,
        {
          id: 'delete-booking',
          duration: 5000,
        }
      );
    }
  };

  // Remove all bookings (for testing purposes)
  const removeAllSampleBookings = async () => {
    if (!firebaseUser) {
      toast.error('🔐 Please sign in first to manage your bookings');
      return;
    }

    try {
      console.log('Removing all bookings...');

      // Show loading toast
      toast.loading('🗑️ Deleting all bookings...', { id: 'bulk-delete' });

      // First check if there are any bookings to delete
      if (bookings.length === 0) {
        toast.info('ℹ️ No bookings found to remove', { id: 'bulk-delete' });
        return;
      }

      const deletedCount = await removeAllUserBookings(firebaseUser.uid);

      if (deletedCount === 0) {
        toast.warning('⚠️ No bookings were deleted. They may have already been removed.', { id: 'bulk-delete' });
        return;
      }

      // Check if all bookings were deleted
      const totalBookings = bookings.length;
      if (deletedCount < totalBookings) {
        toast.success(
          `⚠️ Partial success!\n${deletedCount} of ${totalBookings} bookings deleted.\nSome bookings could not be removed.`,
          {
            id: 'bulk-delete',
            duration: 6000,
          }
        );
      } else {
        toast.success(
          `🎉 All bookings removed successfully!\n${deletedCount} booking${deletedCount > 1 ? 's' : ''} deleted from your account.`,
          {
            id: 'bulk-delete',
            duration: 5000,
          }
        );
      }

      // Refresh bookings to show updated state
      setTimeout(() => {
        console.log('Refreshing bookings after bulk deletion...');
        fetchBookings();
      }, 1500);
    } catch (error: any) {
      console.error('Error removing bookings:', error);

      // Provide more specific error messages
      let errorMessage = 'Some bookings may not have been deleted. Please try again.';
      if (error.message.includes('permission')) {
        errorMessage = 'Permission denied. Please check your account permissions.';
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.message.includes('not found')) {
        errorMessage = 'Some bookings were already deleted or not found.';
      }

      toast.error(
        `❌ Failed to remove all bookings\n${errorMessage}`,
        {
          id: 'bulk-delete',
          duration: 6000,
        }
      );

      // Still refresh to show current state
      setTimeout(() => {
        fetchBookings();
      }, 2000);
    }
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.CONFIRMED:
        return 'bg-green-500';
      case BookingStatus.PENDING:
        return 'bg-yellow-500';
      case BookingStatus.CANCELLED:
        return 'bg-red-500';
      case BookingStatus.CHECKED_IN:
        return 'bg-blue-500';
      case BookingStatus.CHECKED_OUT:
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.CONFIRMED:
        return 'Confirmed';
      case BookingStatus.PENDING:
        return 'Pending';
      case BookingStatus.CANCELLED:
        return 'Cancelled';
      case BookingStatus.CHECKED_IN:
        return 'Checked In';
      case BookingStatus.CHECKED_OUT:
        return 'Checked Out';
      default:
        return status;
    }
  };

  if (!firebaseUser) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <Calendar className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-semibold mb-2">Sign In Required</h3>
          <p className="text-muted-foreground">
            Please sign in to view your booking history.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className ? `booking-container ${className}` : 'booking-container'}>
      <div className="booking-header">
        <div className="booking-header-content">
          <h2 className="booking-title">My Bookings</h2>
          <p className="booking-subtitle">
            View and manage your hotel reservations
          </p>
        </div>
        {!loading && firebaseUser && (
          <div className="booking-actions">
            <div className="booking-actions-group">
              <Button onClick={fetchBookings} variant="outline" size="sm" className="booking-action-button">
                <RefreshCw className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Refresh</span>
                <span className="sm:hidden">Refresh</span>
              </Button>
              <Button onClick={createTestBooking} variant="outline" size="sm" className="booking-action-button">
                <span className="hidden sm:inline">Create Test Booking</span>
                <span className="sm:hidden">Test</span>
              </Button>
            </div>
            <div className="booking-actions-group">
              <Button onClick={createSampleBookingsHandler} variant="outline" size="sm" className="booking-action-button">
                <span className="hidden sm:inline">Create Sample Data</span>
                <span className="sm:hidden">Sample Data</span>
              </Button>
              {bookings.length > 0 && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="booking-action-button text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      <span className="hidden sm:inline">Remove All</span>
                      <span className="sm:hidden">Remove All</span>
                    </Button>
                  </AlertDialogTrigger>
                <AlertDialogContent className="booking-dialog-content bulk-delete">
                  <AlertDialogHeader className="booking-dialog-header">
                    <AlertDialogTitle className="text-lg font-semibold flex items-center justify-center sm:justify-start gap-2">
                      <Trash2 className="w-5 h-5 text-red-600" />
                      Remove All Bookings
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-sm text-muted-foreground mt-3">
                      Are you sure you want to delete{' '}
                      <span className="font-bold text-foreground text-lg">
                        all {bookings.length} booking{bookings.length > 1 ? 's' : ''}
                      </span>
                      ?
                      <br />
                      <br />
                      <span className="booking-dialog-warning">
                        ⚠️ This action cannot be undone
                      </span>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter className="booking-dialog-footer">
                    <AlertDialogCancel className="booking-dialog-button booking-dialog-cancel">
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={removeAllSampleBookings}
                      className="booking-dialog-button booking-dialog-delete"
                    >
                      Delete All {bookings.length} Booking{bookings.length > 1 ? 's' : ''}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="booking-loading">
            <Loader2 className="w-8 h-8 animate-spin" />
            <p className="text-muted-foreground">Loading your bookings...</p>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && !loading && (
        <Card>
          <CardContent className="booking-empty-state">
            <AlertCircle className="booking-empty-icon text-red-500" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Bookings</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <div className="booking-actions-group justify-center">
              <Button onClick={fetchBookings} className="btn-primary">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
              <Button onClick={createSampleBookingsHandler} variant="outline">
                Create Sample Data
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              If you're testing the app, try creating sample bookings to see how it works.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !error && bookings.length === 0 && (
        <Card>
          <CardContent className="booking-empty-state">
            <Calendar className="booking-empty-icon" />
            <h3 className="text-xl font-semibold mb-2">No Bookings Found</h3>
            <p className="text-muted-foreground mb-4">
              You haven't made any bookings yet, or they haven't been loaded properly.
            </p>
            <div className="booking-actions-group justify-center">
              <Button onClick={() => window.location.href = '/#booking'}>
                Book Your Stay
              </Button>
              <Button onClick={createSampleBookingsHandler} variant="outline">
                Create Sample Data
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bookings List */}
      {!loading && !error && bookings.length > 0 && (
        <div className="booking-grid">
          {bookings.map((booking) => (
            <Card key={booking.id} className="booking-card">
              <CardContent className="p-0">
                <div className="booking-card-header">
                  <div>
                    <h3 className="text-xl font-semibold mb-1">
                      {booking.hotelName}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      {booking.rooms?.[0]?.roomType || 'Room'} • {booking.totalRooms} room{booking.totalRooms > 1 ? 's' : ''}
                    </p>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <MapPin className="w-4 h-4" />
                      <span>Booking ID: {booking.id}</span>
                    </div>
                  </div>
                  <Badge className={`booking-status-badge ${getStatusColor(booking.status)} text-white`}>
                    {getStatusText(booking.status)}
                  </Badge>
                </div>

                <div className="booking-card-content text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Check-in</div>
                      <div className="text-muted-foreground">
                        {formatDate(booking.checkIn.toDate())}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Check-out</div>
                      <div className="text-muted-foreground">
                        {formatDate(booking.checkOut.toDate())}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Guests</div>
                      <div className="text-muted-foreground">
                        {booking.adults} adult{booking.adults !== 1 ? 's' : ''}{booking.children > 0 && `, ${booking.children} child${booking.children !== 1 ? 'ren' : ''}`}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <IndianRupee className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Total</div>
                      <div className="text-lg font-bold text-primary">
                        {formatCurrency(booking.totalAmount)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Guest Information */}
                <div className="bg-muted/50 rounded-lg p-3 mb-4">
                  <h4 className="font-medium mb-2 text-sm">Guest Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      <span>{booking.guestName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <span>{booking.guestEmail}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <span>{booking.guestPhone}</span>
                    </div>
                  </div>
                </div>

                {/* Special Requests */}
                {booking.guestNotes && (
                  <div className="mb-4">
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="w-4 h-4 text-muted-foreground" />
                      <span className="font-medium">Special Requests:</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1 pl-6">
                      {booking.guestNotes}
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="booking-card-actions">
                  <p className="text-xs text-muted-foreground">
                    Booked on: {formatDate(booking.createdAt.toDate())}
                  </p>
                  <div>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full sm:w-auto text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 transition-colors"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Booking
                          </Button>
                        </AlertDialogTrigger>
                      <AlertDialogContent className="booking-dialog-content">
                        <AlertDialogHeader className="booking-dialog-header">
                          <AlertDialogTitle className="text-lg font-semibold">
                            Delete Booking
                          </AlertDialogTitle>
                          <AlertDialogDescription className="text-sm text-muted-foreground mt-2">
                            Are you sure you want to delete the booking for{' '}
                            <span className="font-medium text-foreground">"{booking.hotelName}"</span>?
                            <br />
                            <br />
                            <span className="booking-dialog-warning">
                              ⚠️ This action cannot be undone
                            </span>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter className="booking-dialog-footer">
                          <AlertDialogCancel className="booking-dialog-button booking-dialog-cancel">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteBooking(booking.id, booking.hotelName)}
                            className="booking-dialog-button booking-dialog-delete"
                          >
                            Delete Booking
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default StaticBookingHistory;
