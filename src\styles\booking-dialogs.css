/* Booking Dialog and Component Responsive Styles */

/* Ensure dialogs are properly centered and responsive */
[data-radix-popper-content-wrapper] {
  position: fixed !important;
  inset: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1rem !important;
  z-index: 50 !important;
}

/* Responsive Container Utilities */
.booking-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

@media (min-width: 640px) {
  .booking-container {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .booking-container {
    padding: 2rem;
  }
}

/* Responsive Grid System */
.booking-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .booking-grid {
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .booking-grid.md-2-cols {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .booking-grid.lg-3-cols {
    grid-template-columns: repeat(3, 1fr);
  }

  .booking-grid {
    gap: 2rem;
  }
}

/* Responsive Header Layout */
.booking-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 1024px) {
  .booking-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.booking-header-content {
  text-align: center;
}

@media (min-width: 1024px) {
  .booking-header-content {
    text-align: left;
  }
}

/* Responsive Action Buttons */
.booking-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

@media (min-width: 640px) {
  .booking-actions {
    flex-direction: row;
    flex-wrap: wrap;
    width: auto;
  }
}

.booking-actions-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.booking-action-button {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 640px) {
  .booking-action-button {
    flex: none;
    min-width: auto;
  }
}

/* Dialog content responsive sizing */
.booking-dialog-content {
  width: 100% !important;
  max-width: 28rem !important; /* sm:max-w-md */
  margin: 0 1rem !important;
  border-radius: 0.5rem !important;
  background: white !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  position: relative !important;
  max-height: calc(100vh - 2rem) !important;
  overflow-y: auto !important;
}

/* Larger dialog for bulk delete */
.booking-dialog-content.bulk-delete {
  max-width: 32rem !important; /* sm:max-w-lg */
}

/* Dialog header responsive */
.booking-dialog-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem !important;
  text-align: center !important;
}

@media (min-width: 640px) {
  .booking-dialog-header {
    text-align: left !important;
  }
}

/* Dialog footer responsive */
.booking-dialog-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.75rem !important;
}

@media (min-width: 640px) {
  .booking-dialog-footer {
    flex-direction: row !important;
    justify-content: flex-end !important;
    gap: 0.5rem !important;
  }
}

/* Button responsive styles */
.booking-dialog-button {
  width: 100% !important;
  justify-content: center !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease-in-out !important;
}

@media (min-width: 640px) {
  .booking-dialog-button {
    width: auto !important;
    min-width: 5rem !important;
  }
}

/* Cancel button */
.booking-dialog-cancel {
  order: 2 !important;
  background: white !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

.booking-dialog-cancel:hover {
  background: #f9fafb !important;
  border-color: #9ca3af !important;
}

@media (min-width: 640px) {
  .booking-dialog-cancel {
    order: 1 !important;
  }
}

/* Delete button */
.booking-dialog-delete {
  order: 1 !important;
  background: #dc2626 !important;
  color: white !important;
  border: 1px solid #dc2626 !important;
}

.booking-dialog-delete:hover {
  background: #b91c1c !important;
  border-color: #b91c1c !important;
}

@media (min-width: 640px) {
  .booking-dialog-delete {
    order: 2 !important;
  }
}

/* Toast message responsive styles */
.booking-toast {
  max-width: calc(100vw - 2rem) !important;
  word-wrap: break-word !important;
  white-space: pre-line !important;
}

@media (min-width: 640px) {
  .booking-toast {
    max-width: 24rem !important;
  }
}

/* Loading spinner in dialogs */
.booking-dialog-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  padding: 0.5rem !important;
}

/* Warning text styling */
.booking-dialog-warning {
  background: #fef2f2 !important;
  color: #dc2626 !important;
  padding: 0.5rem !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  display: inline-block !important;
  margin-top: 0.5rem !important;
}

/* Mobile-specific adjustments */
@media (max-width: 639px) {
  /* Ensure dialogs don't touch screen edges */
  .booking-dialog-content {
    margin: 1rem !important;
    max-height: calc(100vh - 2rem) !important;
  }
  
  /* Larger touch targets on mobile */
  .booking-dialog-button {
    min-height: 2.75rem !important;
    font-size: 1rem !important;
  }
  
  /* Better spacing on mobile */
  .booking-dialog-header {
    padding: 1rem 1rem 0.75rem 1rem !important;
  }
  
  .booking-dialog-footer {
    padding: 0.75rem 1rem 1rem 1rem !important;
    gap: 0.75rem !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .booking-dialog-content {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }
  
  .booking-dialog-cancel {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
  }
  
  .booking-dialog-cancel:hover {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
  }
  
  .booking-dialog-warning {
    background: #7f1d1d !important;
    color: #fca5a5 !important;
  }
}

/* Animation for dialog appearance */
.booking-dialog-content {
  animation: booking-dialog-appear 0.2s ease-out !important;
}

@keyframes booking-dialog-appear {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus styles for accessibility */
.booking-dialog-button:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* Responsive Booking Cards */
.booking-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease-in-out;
}

.booking-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

@media (min-width: 640px) {
  .booking-card {
    padding: 1.5rem;
  }
}

/* Responsive Card Content */
.booking-card-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .booking-card-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }
}

.booking-card-content {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .booking-card-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .booking-card-content {
    grid-template-columns: repeat(3, 1fr);
  }
}

.booking-card-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

@media (min-width: 640px) {
  .booking-card-actions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

/* Responsive Text and Typography */
.booking-title {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
}

@media (min-width: 640px) {
  .booking-title {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .booking-title {
    font-size: 1.875rem;
  }
}

.booking-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
}

@media (min-width: 640px) {
  .booking-subtitle {
    font-size: 1rem;
  }
}

/* Responsive Status Badges */
.booking-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

@media (min-width: 640px) {
  .booking-status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 1rem;
  }
}

/* Responsive Loading States */
.booking-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

@media (min-width: 640px) {
  .booking-loading {
    padding: 3rem;
  }
}

/* Responsive Empty States */
.booking-empty-state {
  text-align: center;
  padding: 2rem 1rem;
}

@media (min-width: 640px) {
  .booking-empty-state {
    padding: 3rem 2rem;
  }
}

@media (min-width: 1024px) {
  .booking-empty-state {
    padding: 4rem 3rem;
  }
}

.booking-empty-icon {
  width: 3rem;
  height: 3rem;
  margin: 0 auto 1rem;
  color: #9ca3af;
}

@media (min-width: 640px) {
  .booking-empty-icon {
    width: 4rem;
    height: 4rem;
    margin-bottom: 1.5rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .booking-dialog-content {
    border: 2px solid #000 !important;
  }

  .booking-dialog-button {
    border-width: 2px !important;
  }

  .booking-card {
    border-width: 2px !important;
  }
}
