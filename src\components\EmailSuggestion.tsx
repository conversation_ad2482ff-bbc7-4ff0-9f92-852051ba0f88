import React from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lightbulb, ArrowRight } from 'lucide-react';

interface EmailSuggestionProps {
  originalEmail: string;
  suggestedEmail: string;
  onAcceptSuggestion: (email: string) => void;
  className?: string;
}

export const EmailSuggestion: React.FC<EmailSuggestionProps> = ({
  originalEmail,
  suggestedEmail,
  onAcceptSuggestion,
  className = ''
}) => {
  return (
    <Alert className={`border-blue-200 bg-blue-50 ${className}`}>
      <Lightbulb className="h-4 w-4 text-blue-600" />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-blue-800 font-medium mb-1">Did you mean:</p>
          <div className="flex items-center gap-2 text-sm">
            <code className="bg-white px-2 py-1 rounded text-red-600 line-through">
              {originalEmail}
            </code>
            <ArrowRight className="h-3 w-3 text-blue-600" />
            <code className="bg-white px-2 py-1 rounded text-green-600 font-medium">
              {suggestedEmail}
            </code>
          </div>
        </div>
        <Button
          onClick={() => onAcceptSuggestion(suggestedEmail)}
          size="sm"
          className="ml-4 bg-blue-600 hover:bg-blue-700"
        >
          Use This
        </Button>
      </AlertDescription>
    </Alert>
  );
};
