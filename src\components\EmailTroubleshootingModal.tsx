import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { HelpCircle, Mail, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';
import { EmailDiagnostics } from '@/services/passwordResetService';

interface EmailTroubleshootingModalProps {
  email: string;
  diagnostics?: EmailDiagnostics;
  onRetry?: () => void;
  trigger?: React.ReactNode;
}

export const EmailTroubleshootingModal: React.FC<EmailTroubleshootingModalProps> = ({
  email,
  diagnostics,
  onRetry,
  trigger
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <HelpCircle className="h-4 w-4" />
      Email Not Received?
    </Button>
  );

  const getProviderSpecificTips = (provider: string) => {
    const tips: { [key: string]: string[] } = {
      'Gmail': [
        'Check the "Promotions", "Updates", or "Spam" tabs in Gmail',
        'Search for "noreply" or "firebase" in your Gmail search bar',
        'Add <EMAIL> to your contacts',
        'Check if Gmail is blocking emails from new senders'
      ],
      'Yahoo': [
        'Check your Yahoo Mail spam folder',
        'Look in the "Bulk" folder if you have one',
        'Add the sender to your Yahoo contacts',
        'Check Yahoo\'s spam filter settings'
      ],
      'Outlook': [
        'Check your Outlook junk email folder',
        'Look in the "Clutter" or "Other" inbox if using Focused Inbox',
        'Add the sender to your safe senders list',
        'Check Outlook\'s spam protection settings'
      ],
      'Other': [
        'Check your spam/junk folder',
        'Add the sender to your contacts or whitelist',
        'Contact your email provider about blocked emails',
        'Check if your inbox is full'
      ]
    };

    return tips[provider] || tips['Other'];
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto" aria-describedby="troubleshooting-description">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Troubleshooting Guide
          </DialogTitle>
        </DialogHeader>

        <div id="troubleshooting-description" className="sr-only">
          Comprehensive guide to troubleshoot email delivery issues for password reset emails
        </div>

        <div className="space-y-6">
          {/* Email Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Email Information</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Email Address:</span>
                <code className="text-sm bg-white px-2 py-1 rounded">{email}</code>
              </div>
              {diagnostics && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Provider:</span>
                    <Badge variant="secondary">{diagnostics.provider}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Domain:</span>
                    <code className="text-sm bg-white px-2 py-1 rounded">{diagnostics.domain}</code>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Format:</span>
                    <Badge variant={diagnostics.emailFormat === 'valid' ? 'default' : 'destructive'}>
                      {diagnostics.emailFormat}
                    </Badge>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Common Issues */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Most Common Issue:</strong> Password reset emails often end up in spam/junk folders. 
              This is the first place you should check!
            </AlertDescription>
          </Alert>

          {/* Provider-Specific Tips */}
          {diagnostics && (
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                {diagnostics.provider}-Specific Troubleshooting
              </h3>
              <ul className="space-y-2">
                {getProviderSpecificTips(diagnostics.provider).map((tip, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span className="text-sm">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* General Troubleshooting */}
          <div>
            <h3 className="font-semibold mb-3">General Troubleshooting Steps</h3>
            <div className="space-y-3">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium">1. Wait and Check</h4>
                <p className="text-sm text-gray-600">
                  Email delivery can take 5-10 minutes. Check your spam folder first.
                </p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium">2. Add to Safe Senders</h4>
                <p className="text-sm text-gray-600">
                  Add <code><EMAIL></code> to your contacts.
                </p>
              </div>
              <div className="border-l-4 border-yellow-500 pl-4">
                <h4 className="font-medium">3. Check Email Settings</h4>
                <p className="text-sm text-gray-600">
                  Ensure your email provider isn't blocking automated emails.
                </p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-medium">4. Try a Different Email</h4>
                <p className="text-sm text-gray-600">
                  If possible, try using a different email address (Gmail usually works well).
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            {onRetry && (
              <Button onClick={() => { onRetry(); setIsOpen(false); }} className="gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Sending Again
              </Button>
            )}
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </div>

          {/* Additional Help */}
          <Alert>
            <HelpCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Still having issues?</strong> Contact our support team with your email address 
              and we'll help you resolve the problem.
            </AlertDescription>
          </Alert>
        </div>
      </DialogContent>
    </Dialog>
  );
};
