// Booking service for Firestore database operations
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import { db, auth } from '@/lib/firebase';
import { Booking, BookingStatus, PaymentStatus } from '@/types/booking';
import { generateBookingId } from '@/lib/utils';

// Collection names
const BOOKINGS_COLLECTION = 'bookings';
const BOOKING_STATS_COLLECTION = 'booking_stats';
const HOTEL_BOOKINGS_COLLECTION = 'hotel_bookings';

// Create a new booking
export const createBooking = async (bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const batch = writeBatch(db);

    // Generate booking ID
    const bookingId = generateBookingId();

    // Get current user ID from Firebase Auth
    const currentUser = auth.currentUser;

    // Prepare booking document with proper user association
    const booking: Booking = {
      ...bookingData,
      id: bookingId,
      // Ensure userId is set for authenticated users
      userId: bookingData.userId || currentUser?.uid,
      // Add createdBy field for additional tracking
      createdBy: currentUser?.uid,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      status: BookingStatus.PENDING,
      paymentStatus: PaymentStatus.PENDING
    };

    // Add booking to main collection
    const bookingRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    batch.set(bookingRef, booking);

    // Add to hotel-specific bookings for quick queries
    const hotelBookingRef = doc(db, HOTEL_BOOKINGS_COLLECTION, `${booking.hotelId}_${bookingId}`);
    batch.set(hotelBookingRef, {
      bookingId,
      hotelId: booking.hotelId,
      userId: booking.userId,
      checkIn: booking.checkIn,
      checkOut: booking.checkOut,
      status: booking.status,
      totalAmount: booking.totalAmount,
      createdAt: booking.createdAt
    });

    // Update booking statistics
    const statsRef = doc(db, BOOKING_STATS_COLLECTION, 'global');
    batch.update(statsRef, {
      totalBookings: increment(1),
      pendingBookings: increment(1),
      totalRevenue: increment(booking.totalAmount),
      lastUpdated: Timestamp.now()
    });

    // Update hotel-specific statistics
    const hotelStatsRef = doc(db, BOOKING_STATS_COLLECTION, `hotel_${booking.hotelId}`);
    batch.update(hotelStatsRef, {
      totalBookings: increment(1),
      pendingBookings: increment(1),
      totalRevenue: increment(booking.totalAmount),
      lastUpdated: Timestamp.now()
    });

    await batch.commit();
    
    console.log('Booking created successfully:', bookingId);
    return bookingId;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw new Error('Failed to create booking');
  }
};

// Get booking by ID
export const getBooking = async (bookingId: string): Promise<Booking | null> => {
  try {
    const bookingDoc = await getDoc(doc(db, BOOKINGS_COLLECTION, bookingId));
    
    if (!bookingDoc.exists()) {
      return null;
    }

    return { id: bookingDoc.id, ...bookingDoc.data() } as Booking;
  } catch (error) {
    console.error('Error getting booking:', error);
    throw new Error('Failed to get booking');
  }
};

// Get user bookings - enhanced with better debugging
export const getUserBookings = async (
  userId: string,
  pageSize: number = 10,
  lastBookingId?: string
): Promise<{ bookings: Booking[]; hasMore: boolean }> => {
  try {
    console.log('=== ENHANCED BOOKING FETCH ===');
    console.log('Getting bookings for userId:', userId);

    // Get current user email
    const currentUser = auth.currentUser;
    const userEmail = currentUser?.email;
    console.log('Current user email:', userEmail);

    // First, let's check if there are ANY bookings in the database
    console.log('Checking total bookings in database...');
    const totalBookingsQuery = query(collection(db, BOOKINGS_COLLECTION), limit(5));
    const totalSnapshot = await getDocs(totalBookingsQuery);
    console.log('Total bookings in database:', totalSnapshot.size);

    if (totalSnapshot.size === 0) {
      console.log('No bookings found in database at all');
      return { bookings: [], hasMore: false };
    }

    // Log some sample bookings for debugging
    totalSnapshot.forEach((doc, index) => {
      const data = doc.data();
      console.log(`Sample booking ${index + 1}:`, {
        id: doc.id,
        userId: data.userId,
        guestEmail: data.guestEmail,
        createdBy: data.createdBy,
        hotelName: data.hotelName
      });
    });

    // Try the simplest possible query first - just by userId
    console.log('Trying simple userId query...');

    try {
      const userIdQuery = query(
        collection(db, BOOKINGS_COLLECTION),
        where('userId', '==', userId)
      );

      const userIdSnapshot = await getDocs(userIdQuery);
      console.log('UserId query returned:', userIdSnapshot.size, 'documents');

      const userBookings: Booking[] = [];
      userIdSnapshot.forEach((doc) => {
        const bookingData = { id: doc.id, ...doc.data() } as Booking;
        userBookings.push(bookingData);
        console.log('Found booking by userId:', doc.id, bookingData.hotelName);
      });

      if (userBookings.length > 0) {
        console.log('Success with userId query!');
        return { bookings: userBookings.slice(0, pageSize), hasMore: userBookings.length > pageSize };
      }
    } catch (userIdError) {
      console.error('UserId query failed:', userIdError);
    }

    // If userId query fails or returns no results, try email query
    if (userEmail) {
      console.log('Trying guestEmail query...');

      try {
        const emailQuery = query(
          collection(db, BOOKINGS_COLLECTION),
          where('guestEmail', '==', userEmail)
        );

        const emailSnapshot = await getDocs(emailQuery);
        console.log('Email query returned:', emailSnapshot.size, 'documents');

        const emailBookings: Booking[] = [];
        emailSnapshot.forEach((doc) => {
          const bookingData = { id: doc.id, ...doc.data() } as Booking;
          emailBookings.push(bookingData);
          console.log('Found booking by email:', doc.id, bookingData.hotelName);
        });

        if (emailBookings.length > 0) {
          console.log('Success with email query!');
          return { bookings: emailBookings.slice(0, pageSize), hasMore: emailBookings.length > pageSize };
        }
      } catch (emailError) {
        console.error('Email query failed:', emailError);
      }
    }

    // If both specific queries fail, try to get all bookings and filter client-side
    console.log('Trying to get all bookings and filter client-side...');

    try {
      const allBookingsQuery = query(
        collection(db, BOOKINGS_COLLECTION),
        limit(100) // Limit to prevent too much data
      );

      const allSnapshot = await getDocs(allBookingsQuery);
      console.log('All bookings query returned:', allSnapshot.size, 'documents');

      const filteredBookings: Booking[] = [];
      allSnapshot.forEach((doc) => {
        const bookingData = { id: doc.id, ...doc.data() } as Booking;

        // Filter client-side
        if (bookingData.userId === userId ||
            bookingData.guestEmail === userEmail ||
            bookingData.createdBy === userId) {
          filteredBookings.push(bookingData);
          console.log('Found matching booking:', doc.id, bookingData.hotelName);
        }
      });

      console.log('Client-side filtering found:', filteredBookings.length, 'bookings');

      // Sort by creation date
      filteredBookings.sort((a, b) => {
        const aTime = a.createdAt?.toMillis?.() || 0;
        const bTime = b.createdAt?.toMillis?.() || 0;
        return bTime - aTime;
      });

      return {
        bookings: filteredBookings.slice(0, pageSize),
        hasMore: filteredBookings.length > pageSize
      };

    } catch (allBookingsError) {
      console.error('All bookings query failed:', allBookingsError);
      throw allBookingsError;
    }

  } catch (error) {
    console.error('=== BOOKING FETCH ERROR ===');
    console.error('Error getting user bookings:', error);
    throw new Error(`Failed to get user bookings: ${error.message}`);
  }
};

// Get hotel bookings
export const getHotelBookings = async (
  hotelId: string,
  pageSize: number = 20,
  status?: BookingStatus
): Promise<Booking[]> => {
  try {
    let bookingsQuery = query(
      collection(db, BOOKINGS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('createdAt', 'desc'),
      limit(pageSize)
    );

    if (status) {
      bookingsQuery = query(
        collection(db, BOOKINGS_COLLECTION),
        where('hotelId', '==', hotelId),
        where('status', '==', status),
        orderBy('createdAt', 'desc'),
        limit(pageSize)
      );
    }

    const querySnapshot = await getDocs(bookingsQuery);
    const bookings: Booking[] = [];
    
    querySnapshot.forEach((doc) => {
      bookings.push({ id: doc.id, ...doc.data() } as Booking);
    });

    return bookings;
  } catch (error) {
    console.error('Error getting hotel bookings:', error);
    throw new Error('Failed to get hotel bookings');
  }
};

// Create sample bookings for testing
export const createSampleBookings = async (userId: string, userEmail: string): Promise<void> => {
  try {
    console.log('Creating sample bookings for user:', userId, userEmail);

    const sampleBookings = [
      {
        hotelId: 'hotel-arunachala-heritage',
        hotelName: 'Arunachala Heritage Hotel',
        guestName: 'Demo User',
        guestEmail: userEmail,
        guestPhone: '+91 9876543210',
        checkIn: Timestamp.fromDate(new Date('2024-01-15')),
        checkOut: Timestamp.fromDate(new Date('2024-01-17')),
        nights: 2,
        rooms: [{
          roomId: 'room-deluxe-001',
          roomType: 'Deluxe Room',
          quantity: 1,
          pricePerNight: 3500,
          totalPrice: 7000,
          amenities: ['WiFi', 'AC', 'TV', 'Temple View'],
          maxOccupancy: 2
        }],
        totalRooms: 1,
        totalGuests: 2,
        adults: 2,
        children: 0,
        subtotal: 7000,
        taxes: 840,
        fees: 200,
        totalAmount: 8040,
        currency: 'INR',
        status: 'confirmed',
        paymentStatus: 'paid',
        bookingSource: 'website',
        userId: userId,
        createdBy: userId,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        hotelId: 'hotel-temple-view',
        hotelName: 'Temple View Resort',
        guestName: 'Demo User',
        guestEmail: userEmail,
        guestPhone: '+91 9876543210',
        checkIn: Timestamp.fromDate(new Date('2024-02-10')),
        checkOut: Timestamp.fromDate(new Date('2024-02-12')),
        nights: 2,
        rooms: [{
          roomId: 'room-suite-001',
          roomType: 'Premium Suite',
          quantity: 1,
          pricePerNight: 5000,
          totalPrice: 10000,
          amenities: ['WiFi', 'AC', 'TV', 'Balcony', 'Mini Bar'],
          maxOccupancy: 3
        }],
        totalRooms: 1,
        totalGuests: 2,
        adults: 2,
        children: 0,
        subtotal: 10000,
        taxes: 1200,
        fees: 300,
        totalAmount: 11500,
        currency: 'INR',
        status: 'completed',
        paymentStatus: 'paid',
        bookingSource: 'website',
        userId: userId,
        createdBy: userId,
        createdAt: Timestamp.fromDate(new Date('2024-02-01')),
        updatedAt: Timestamp.fromDate(new Date('2024-02-01'))
      }
    ];

    const batch = writeBatch(db);

    sampleBookings.forEach((booking, index) => {
      const bookingRef = doc(collection(db, BOOKINGS_COLLECTION));
      batch.set(bookingRef, booking);
      console.log(`Added sample booking ${index + 1}:`, booking.hotelName);
    });

    await batch.commit();
    console.log('Sample bookings created successfully');

  } catch (error) {
    console.error('Error creating sample bookings:', error);
    throw error;
  }
};

// Remove all bookings for a user (for testing/cleanup purposes)
export const removeAllUserBookings = async (userId: string): Promise<number> => {
  try {
    console.log('Removing all bookings for user:', userId);

    // Get all user bookings
    const userBookings = await getUserBookings(userId, 1000); // Get up to 1000 bookings

    if (userBookings.bookings.length === 0) {
      console.log('No bookings found to remove');
      return 0;
    }

    console.log(`Found ${userBookings.bookings.length} bookings to remove`);

    // Delete each booking individually with error handling
    let successCount = 0;
    let failureCount = 0;
    const errors: string[] = [];

    for (const booking of userBookings.bookings) {
      try {
        console.log(`Deleting booking: ${booking.id} - ${booking.hotelName}`);
        await deleteBooking(booking.id);
        successCount++;
        console.log(`Successfully deleted booking: ${booking.id}`);
      } catch (error: any) {
        failureCount++;
        const errorMsg = `Failed to delete ${booking.hotelName} (${booking.id}): ${error.message}`;
        console.error(errorMsg);
        errors.push(errorMsg);

        // Continue with other bookings even if one fails
        continue;
      }
    }

    console.log(`Deletion complete: ${successCount} successful, ${failureCount} failed`);

    if (failureCount > 0) {
      console.warn('Some bookings failed to delete:', errors);
      if (successCount === 0) {
        throw new Error(`Failed to delete any bookings. Errors: ${errors.join('; ')}`);
      } else {
        console.warn(`Partial success: ${successCount} deleted, ${failureCount} failed`);
      }
    }

    return successCount;

  } catch (error: any) {
    console.error('Error removing all user bookings:', error);
    throw new Error(error.message || 'Failed to remove all bookings');
  }
};

// Update booking status
export const updateBookingStatus = async (
  bookingId: string,
  status: BookingStatus,
  adminNotes?: string
): Promise<void> => {
  try {
    const batch = writeBatch(db);
    
    // Get current booking
    const bookingDoc = await getDoc(doc(db, BOOKINGS_COLLECTION, bookingId));
    if (!bookingDoc.exists()) {
      throw new Error('Booking not found');
    }

    const currentBooking = bookingDoc.data() as Booking;
    const oldStatus = currentBooking.status;

    // Update main booking document
    const bookingRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    batch.update(bookingRef, {
      status,
      adminNotes: adminNotes || currentBooking.adminNotes,
      updatedAt: Timestamp.now()
    });

    // Update hotel booking reference
    const hotelBookingRef = doc(db, HOTEL_BOOKINGS_COLLECTION, `${currentBooking.hotelId}_${bookingId}`);
    batch.update(hotelBookingRef, {
      status,
      updatedAt: Timestamp.now()
    });

    // Update statistics based on status change
    if (oldStatus !== status) {
      const statsRef = doc(db, BOOKING_STATS_COLLECTION, 'global');
      const hotelStatsRef = doc(db, BOOKING_STATS_COLLECTION, `hotel_${currentBooking.hotelId}`);

      // Decrement old status count
      if (oldStatus === BookingStatus.PENDING) {
        batch.update(statsRef, { pendingBookings: increment(-1) });
        batch.update(hotelStatsRef, { pendingBookings: increment(-1) });
      } else if (oldStatus === BookingStatus.CONFIRMED) {
        batch.update(statsRef, { confirmedBookings: increment(-1) });
        batch.update(hotelStatsRef, { confirmedBookings: increment(-1) });
      } else if (oldStatus === BookingStatus.CANCELLED) {
        batch.update(statsRef, { cancelledBookings: increment(-1) });
        batch.update(hotelStatsRef, { cancelledBookings: increment(-1) });
      }

      // Increment new status count
      if (status === BookingStatus.PENDING) {
        batch.update(statsRef, { pendingBookings: increment(1) });
        batch.update(hotelStatsRef, { pendingBookings: increment(1) });
      } else if (status === BookingStatus.CONFIRMED) {
        batch.update(statsRef, { confirmedBookings: increment(1) });
        batch.update(hotelStatsRef, { confirmedBookings: increment(1) });
      } else if (status === BookingStatus.CANCELLED) {
        batch.update(statsRef, { cancelledBookings: increment(1) });
        batch.update(hotelStatsRef, { cancelledBookings: increment(1) });
      }

      batch.update(statsRef, { lastUpdated: Timestamp.now() });
      batch.update(hotelStatsRef, { lastUpdated: Timestamp.now() });
    }

    await batch.commit();
    console.log('Booking status updated successfully:', bookingId, status);
  } catch (error) {
    console.error('Error updating booking status:', error);
    throw new Error('Failed to update booking status');
  }
};

// Update payment status
export const updatePaymentStatus = async (
  bookingId: string,
  paymentStatus: PaymentStatus,
  paymentDetails?: any
): Promise<void> => {
  try {
    const bookingRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    
    const updateData: any = {
      paymentStatus,
      updatedAt: Timestamp.now()
    };

    if (paymentDetails) {
      updateData.paymentDetails = paymentDetails;
    }

    await updateDoc(bookingRef, updateData);
    console.log('Payment status updated successfully:', bookingId, paymentStatus);
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw new Error('Failed to update payment status');
  }
};

// Cancel booking
export const cancelBooking = async (bookingId: string, reason?: string): Promise<void> => {
  try {
    await updateBookingStatus(bookingId, BookingStatus.CANCELLED, reason);
    console.log('Booking cancelled successfully:', bookingId);
  } catch (error) {
    console.error('Error cancelling booking:', error);
    throw new Error('Failed to cancel booking');
  }
};

// Delete booking - simplified and more robust
export const deleteBooking = async (bookingId: string): Promise<void> => {
  try {
    console.log('Starting deletion for booking:', bookingId);

    // Get booking details first
    const bookingDoc = await getDoc(doc(db, BOOKINGS_COLLECTION, bookingId));
    if (!bookingDoc.exists()) {
      console.warn('Booking not found:', bookingId);
      throw new Error(`Booking ${bookingId} not found`);
    }

    const booking = bookingDoc.data() as Booking;
    console.log('Found booking to delete:', booking.hotelName, booking.id);

    // Simple deletion - just delete the main booking document
    const bookingRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    await deleteDoc(bookingRef);

    console.log('Main booking document deleted successfully:', bookingId);

    // Try to clean up related data, but don't fail if it doesn't work
    try {
      // Try to delete hotel booking reference if it exists
      const hotelBookingRef = doc(db, HOTEL_BOOKINGS_COLLECTION, `${booking.hotelId}_${bookingId}`);
      const hotelBookingDoc = await getDoc(hotelBookingRef);
      if (hotelBookingDoc.exists()) {
        await deleteDoc(hotelBookingRef);
        console.log('Hotel booking reference deleted:', `${booking.hotelId}_${bookingId}`);
      }
    } catch (cleanupError: any) {
      console.warn('Failed to delete hotel booking reference, but continuing:', cleanupError.message);
    }

    // Try to update statistics, but don't fail if it doesn't work
    try {
      const batch = writeBatch(db);
      let hasUpdates = false;

      // Try to update global statistics if they exist
      const statsRef = doc(db, BOOKING_STATS_COLLECTION, 'global');
      const statsDoc = await getDoc(statsRef);
      if (statsDoc.exists()) {
        batch.update(statsRef, {
          totalBookings: increment(-1),
          totalRevenue: increment(-booking.totalAmount),
          lastUpdated: Timestamp.now()
        });
        hasUpdates = true;
      }

      // Try to update hotel statistics if they exist
      const hotelStatsRef = doc(db, BOOKING_STATS_COLLECTION, `hotel_${booking.hotelId}`);
      const hotelStatsDoc = await getDoc(hotelStatsRef);
      if (hotelStatsDoc.exists()) {
        batch.update(hotelStatsRef, {
          totalBookings: increment(-1),
          totalRevenue: increment(-booking.totalAmount),
          lastUpdated: Timestamp.now()
        });
        hasUpdates = true;
      }

      // Commit statistics updates if any exist
      if (hasUpdates) {
        await batch.commit();
        console.log('Statistics updated for booking deletion:', bookingId);
      }
    } catch (statsError: any) {
      console.warn('Failed to update statistics, but booking was deleted:', statsError.message);
    }

    console.log('Booking deletion completed successfully:', bookingId);

  } catch (error: any) {
    console.error('Error deleting booking:', bookingId, error);
    throw new Error(`Failed to delete booking: ${error.message}`);
  }
};

// Get booking statistics
export const getBookingStats = async (hotelId?: string) => {
  try {
    const statsId = hotelId ? `hotel_${hotelId}` : 'global';
    const statsDoc = await getDoc(doc(db, BOOKING_STATS_COLLECTION, statsId));
    
    if (!statsDoc.exists()) {
      // Initialize stats if they don't exist
      const initialStats = {
        totalBookings: 0,
        pendingBookings: 0,
        confirmedBookings: 0,
        cancelledBookings: 0,
        totalRevenue: 0,
        lastUpdated: Timestamp.now()
      };
      
      await updateDoc(doc(db, BOOKING_STATS_COLLECTION, statsId), initialStats);
      return initialStats;
    }

    return statsDoc.data();
  } catch (error) {
    console.error('Error getting booking stats:', error);
    throw new Error('Failed to get booking statistics');
  }
};

// Search bookings (admin function)
export const searchBookings = async (
  searchTerm: string,
  filters?: {
    status?: BookingStatus;
    hotelId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }
): Promise<Booking[]> => {
  try {
    let bookingsQuery = query(
      collection(db, BOOKINGS_COLLECTION),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    if (filters?.status) {
      bookingsQuery = query(
        collection(db, BOOKINGS_COLLECTION),
        where('status', '==', filters.status),
        orderBy('createdAt', 'desc'),
        limit(50)
      );
    }

    if (filters?.hotelId) {
      bookingsQuery = query(
        collection(db, BOOKINGS_COLLECTION),
        where('hotelId', '==', filters.hotelId),
        orderBy('createdAt', 'desc'),
        limit(50)
      );
    }

    const querySnapshot = await getDocs(bookingsQuery);
    const bookings: Booking[] = [];
    
    querySnapshot.forEach((doc) => {
      const booking = { id: doc.id, ...doc.data() } as Booking;
      
      // Client-side filtering for search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = 
          booking.id.toLowerCase().includes(searchLower) ||
          booking.guestName.toLowerCase().includes(searchLower) ||
          booking.guestEmail.toLowerCase().includes(searchLower) ||
          booking.guestPhone.toLowerCase().includes(searchLower);
        
        if (matchesSearch) {
          bookings.push(booking);
        }
      } else {
        bookings.push(booking);
      }
    });

    return bookings;
  } catch (error) {
    console.error('Error searching bookings:', error);
    throw new Error('Failed to search bookings');
  }
};
