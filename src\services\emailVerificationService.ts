// Email Verification Service for validating email addresses
import { fetchSignInMethodsForEmail } from 'firebase/auth';
import { auth } from '@/lib/firebase';

export interface EmailValidationResult {
  isValid: boolean;
  exists: boolean;
  provider: string;
  domain: string;
  suggestions: string[];
  confidence: 'high' | 'medium' | 'low';
  issues: string[];
}

/**
 * Common email domains and their variations
 */
const COMMON_DOMAINS = {
  'gmail.com': ['gmail.com', 'googlemail.com'],
  'yahoo.com': ['yahoo.com', 'ymail.com', 'rocketmail.com'],
  'outlook.com': ['outlook.com', 'hotmail.com', 'live.com', 'msn.com'],
  'icloud.com': ['icloud.com', 'me.com', 'mac.com'],
  'aol.com': ['aol.com'],
  'protonmail.com': ['protonmail.com', 'pm.me']
};

/**
 * Common typos in email domains
 */
const DOMAIN_TYPOS = {
  'gmial.com': 'gmail.com',
  'gmai.com': 'gmail.com',
  'gmail.co': 'gmail.com',
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'outlok.com': 'outlook.com',
  'hotmial.com': 'hotmail.com',
  'hotmai.com': 'hotmail.com'
};

/**
 * Validate email format using comprehensive regex
 */
const validateEmailFormat = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
};

/**
 * Extract domain from email address
 */
const extractDomain = (email: string): string => {
  const parts = email.split('@');
  return parts.length === 2 ? parts[1].toLowerCase() : '';
};

/**
 * Get email provider from domain
 */
const getEmailProvider = (domain: string): string => {
  for (const [provider, domains] of Object.entries(COMMON_DOMAINS)) {
    if (domains.includes(domain)) {
      return provider.split('.')[0].charAt(0).toUpperCase() + provider.split('.')[0].slice(1);
    }
  }
  return 'Other';
};

/**
 * Generate suggestions for common typos
 */
const generateSuggestions = (email: string): string[] => {
  const suggestions: string[] = [];
  const [localPart, domain] = email.split('@');
  
  if (!localPart || !domain) return suggestions;
  
  // Check for domain typos
  const correctedDomain = DOMAIN_TYPOS[domain.toLowerCase()];
  if (correctedDomain) {
    suggestions.push(`${localPart}@${correctedDomain}`);
  }
  
  // Check for missing common domains
  const domainParts = domain.split('.');
  if (domainParts.length === 1) {
    // Missing TLD
    suggestions.push(`${localPart}@${domain}.com`);
    if (domain.includes('gmail') || domain.includes('google')) {
      suggestions.push(`${localPart}@gmail.com`);
    }
    if (domain.includes('yahoo')) {
      suggestions.push(`${localPart}@yahoo.com`);
    }
    if (domain.includes('outlook') || domain.includes('hotmail')) {
      suggestions.push(`${localPart}@outlook.com`);
    }
  }
  
  return suggestions.slice(0, 3); // Limit to 3 suggestions
};

/**
 * Check if email exists in Firebase Auth
 */
const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const signInMethods = await fetchSignInMethodsForEmail(auth, email);
    return signInMethods.length > 0;
  } catch (error: any) {
    // If we get an error, it might be due to invalid email format
    // or network issues. We'll assume the email might exist.
    console.warn('Error checking email existence:', error);
    return true; // Assume it exists to avoid false negatives
  }
};

/**
 * Validate email address comprehensively
 */
export const validateEmailAddress = async (email: string): Promise<EmailValidationResult> => {
  const trimmedEmail = email.trim().toLowerCase();
  
  // Basic format validation
  const isValidFormat = validateEmailFormat(trimmedEmail);
  const domain = extractDomain(trimmedEmail);
  const provider = getEmailProvider(domain);
  
  const issues: string[] = [];
  const suggestions = generateSuggestions(trimmedEmail);
  
  // Check format issues
  if (!isValidFormat) {
    issues.push('Invalid email format');
  }
  
  if (!domain) {
    issues.push('Missing domain');
  }
  
  if (domain && !domain.includes('.')) {
    issues.push('Domain missing top-level domain (e.g., .com)');
  }
  
  // Check for common issues
  if (trimmedEmail.includes('..')) {
    issues.push('Contains consecutive dots');
  }
  
  if (trimmedEmail.startsWith('.') || trimmedEmail.endsWith('.')) {
    issues.push('Starts or ends with a dot');
  }
  
  if (trimmedEmail.includes(' ')) {
    issues.push('Contains spaces');
  }
  
  // Check if email exists (only if format is valid)
  let exists = false;
  if (isValidFormat) {
    exists = await checkEmailExists(trimmedEmail);
  }
  
  // Determine confidence level
  let confidence: 'high' | 'medium' | 'low' = 'low';
  if (isValidFormat && exists && issues.length === 0) {
    confidence = 'high';
  } else if (isValidFormat && issues.length <= 1) {
    confidence = 'medium';
  }
  
  return {
    isValid: isValidFormat,
    exists,
    provider,
    domain,
    suggestions,
    confidence,
    issues
  };
};

/**
 * Validate multiple email addresses
 */
export const validateEmailAddresses = async (emails: string[]): Promise<EmailValidationResult[]> => {
  const results = await Promise.all(
    emails.map(email => validateEmailAddress(email))
  );
  return results;
};

/**
 * Get email validation summary
 */
export const getEmailValidationSummary = (results: EmailValidationResult[]) => {
  const total = results.length;
  const valid = results.filter(r => r.isValid).length;
  const existing = results.filter(r => r.exists).length;
  const highConfidence = results.filter(r => r.confidence === 'high').length;
  
  return {
    total,
    valid,
    existing,
    highConfidence,
    validPercentage: total > 0 ? Math.round((valid / total) * 100) : 0,
    existingPercentage: total > 0 ? Math.round((existing / total) * 100) : 0,
    highConfidencePercentage: total > 0 ? Math.round((highConfidence / total) * 100) : 0
  };
};

/**
 * Quick email validation for forms
 */
export const quickEmailValidation = (email: string): {
  isValid: boolean;
  suggestion?: string;
  issue?: string;
} => {
  const trimmedEmail = email.trim().toLowerCase();
  
  if (!trimmedEmail) {
    return { isValid: false, issue: 'Email is required' };
  }
  
  if (!validateEmailFormat(trimmedEmail)) {
    const suggestions = generateSuggestions(trimmedEmail);
    return {
      isValid: false,
      issue: 'Invalid email format',
      suggestion: suggestions[0]
    };
  }
  
  return { isValid: true };
};
