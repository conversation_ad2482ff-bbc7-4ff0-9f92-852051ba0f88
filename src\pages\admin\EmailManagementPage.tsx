import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Mail, 
  Settings, 
  BarChart3, 
  Users, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Download
} from 'lucide-react';
import { EmailDiagnosticsTool } from '@/components/admin/EmailDiagnosticsTool';

export const EmailManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('diagnostics');

  // Mock data for demonstration
  const emailStats = {
    totalSent: 1247,
    delivered: 1156,
    failed: 91,
    deliveryRate: 92.7,
    commonIssues: [
      { issue: 'Spam Folder', count: 45, percentage: 49.5 },
      { issue: 'Invalid Email', count: 23, percentage: 25.3 },
      { issue: 'Provider Blocking', count: 15, percentage: 16.5 },
      { issue: 'Network Issues', count: 8, percentage: 8.7 }
    ],
    providerStats: [
      { provider: 'Gmail', sent: 623, delivered: 598, rate: 96.0 },
      { provider: 'Yahoo', sent: 234, delivered: 201, rate: 85.9 },
      { provider: 'Outlook', sent: 198, delivered: 186, rate: 93.9 },
      { provider: 'Other', sent: 192, delivered: 171, rate: 89.1 }
    ]
  };

  const getStatusColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600 bg-green-100';
    if (rate >= 85) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Email Management</h1>
          <p className="text-gray-600 mt-1">
            Monitor and manage password reset email delivery
          </p>
        </div>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Export Report
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="diagnostics" className="gap-2">
            <Settings className="h-4 w-4" />
            Diagnostics
          </TabsTrigger>
          <TabsTrigger value="analytics" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="providers" className="gap-2">
            <Mail className="h-4 w-4" />
            Providers
          </TabsTrigger>
          <TabsTrigger value="users" className="gap-2">
            <Users className="h-4 w-4" />
            User Issues
          </TabsTrigger>
        </TabsList>

        <TabsContent value="diagnostics" className="space-y-6">
          <EmailDiagnosticsTool />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Sent</p>
                    <p className="text-2xl font-bold text-gray-900">{emailStats.totalSent.toLocaleString()}</p>
                  </div>
                  <Mail className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Delivered</p>
                    <p className="text-2xl font-bold text-green-600">{emailStats.delivered.toLocaleString()}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Failed</p>
                    <p className="text-2xl font-bold text-red-600">{emailStats.failed.toLocaleString()}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Delivery Rate</p>
                    <p className="text-2xl font-bold text-blue-600">{emailStats.deliveryRate}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Common Issues */}
          <Card>
            <CardHeader>
              <CardTitle>Common Delivery Issues</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {emailStats.commonIssues.map((issue, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-500" />
                      <div>
                        <p className="font-medium">{issue.issue}</p>
                        <p className="text-sm text-gray-600">{issue.count} occurrences</p>
                      </div>
                    </div>
                    <Badge variant="secondary">{issue.percentage}%</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="providers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Email Provider Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {emailStats.providerStats.map((provider, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="font-medium">{provider.provider}</p>
                        <p className="text-sm text-gray-600">
                          {provider.delivered}/{provider.sent} delivered
                        </p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(provider.rate)}>
                      {provider.rate}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Provider Recommendations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium text-green-800">Gmail (Best Performance)</h4>
                <p className="text-sm text-green-600">
                  Highest delivery rate. Recommend for critical communications.
                </p>
              </div>
              <div className="border-l-4 border-yellow-500 pl-4">
                <h4 className="font-medium text-yellow-800">Yahoo (Moderate Issues)</h4>
                <p className="text-sm text-yellow-600">
                  Strict spam filtering. Users should check spam folder and bulk mail.
                </p>
              </div>
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium text-blue-800">Outlook (Good Performance)</h4>
                <p className="text-sm text-blue-600">
                  Generally reliable. Users should check junk folder and clutter inbox.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent User Issues</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>User issue tracking will be available in the next update.</p>
                  <p className="text-sm">This will show recent email delivery problems reported by users.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
