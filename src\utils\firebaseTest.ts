// Firebase Connection Test Utility
import { auth, db, storage, analytics } from '@/lib/firebase';
import { 
  signInAnonymously, 
  signOut, 
  onAuthStateChanged,
  User 
} from 'firebase/auth';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  deleteDoc,
  Timestamp 
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';
import { logEvent } from 'firebase/analytics';

export interface FirebaseTestResult {
  service: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

/**
 * Test Firebase Authentication
 */
export const testFirebaseAuth = async (): Promise<FirebaseTestResult> => {
  try {
    console.log('Testing Firebase Authentication...');
    
    // Test anonymous sign in
    const userCredential = await signInAnonymously(auth);
    const user = userCredential.user;
    
    if (user) {
      console.log('Anonymous sign-in successful:', user.uid);
      
      // Sign out the anonymous user
      await signOut(auth);
      console.log('Sign-out successful');
      
      return {
        service: 'Authentication',
        status: 'success',
        message: 'Firebase Auth is working correctly',
        details: { uid: user.uid }
      };
    } else {
      throw new Error('No user returned from sign-in');
    }
  } catch (error: any) {
    console.error('Firebase Auth test failed:', error);
    return {
      service: 'Authentication',
      status: 'error',
      message: `Auth test failed: ${error.message}`,
      details: error
    };
  }
};

/**
 * Test Firestore Database
 */
export const testFirestore = async (): Promise<FirebaseTestResult> => {
  try {
    console.log('Testing Firestore Database...');
    
    const testDocId = `test_${Date.now()}`;
    const testData = {
      message: 'Firebase connection test',
      timestamp: Timestamp.now(),
      testId: testDocId
    };
    
    // Test write operation
    const docRef = doc(db, 'connection_tests', testDocId);
    await setDoc(docRef, testData);
    console.log('Firestore write successful');
    
    // Test read operation
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      console.log('Firestore read successful:', docSnap.data());
      
      // Clean up test document
      await deleteDoc(docRef);
      console.log('Test document cleaned up');
      
      return {
        service: 'Firestore',
        status: 'success',
        message: 'Firestore is working correctly',
        details: { testDocId, data: docSnap.data() }
      };
    } else {
      throw new Error('Test document not found after write');
    }
  } catch (error: any) {
    console.error('Firestore test failed:', error);
    return {
      service: 'Firestore',
      status: 'error',
      message: `Firestore test failed: ${error.message}`,
      details: error
    };
  }
};

/**
 * Test Firebase Storage
 */
export const testFirebaseStorage = async (): Promise<FirebaseTestResult> => {
  try {
    console.log('Testing Firebase Storage...');
    
    const testFileName = `test_${Date.now()}.txt`;
    const testContent = 'Firebase Storage connection test';
    const testBlob = new Blob([testContent], { type: 'text/plain' });
    
    // Test upload
    const storageRef = ref(storage, `connection_tests/${testFileName}`);
    const uploadResult = await uploadBytes(storageRef, testBlob);
    console.log('Storage upload successful:', uploadResult.metadata.name);
    
    // Test download URL generation
    const downloadURL = await getDownloadURL(storageRef);
    console.log('Download URL generated:', downloadURL);
    
    // Clean up test file
    await deleteObject(storageRef);
    console.log('Test file cleaned up');
    
    return {
      service: 'Storage',
      status: 'success',
      message: 'Firebase Storage is working correctly',
      details: { fileName: testFileName, downloadURL }
    };
  } catch (error: any) {
    console.error('Firebase Storage test failed:', error);
    return {
      service: 'Storage',
      status: 'error',
      message: `Storage test failed: ${error.message}`,
      details: error
    };
  }
};

/**
 * Test Firebase Analytics
 */
export const testFirebaseAnalytics = async (): Promise<FirebaseTestResult> => {
  try {
    console.log('Testing Firebase Analytics...');
    
    if (!analytics) {
      return {
        service: 'Analytics',
        status: 'warning',
        message: 'Analytics not initialized (likely running in server environment)',
        details: { reason: 'Analytics only works in browser environment' }
      };
    }
    
    // Test custom event logging
    logEvent(analytics, 'firebase_connection_test', {
      test_timestamp: new Date().toISOString(),
      test_type: 'connection_validation'
    });
    
    console.log('Analytics event logged successfully');
    
    return {
      service: 'Analytics',
      status: 'success',
      message: 'Firebase Analytics is working correctly',
      details: { eventLogged: 'firebase_connection_test' }
    };
  } catch (error: any) {
    console.error('Firebase Analytics test failed:', error);
    return {
      service: 'Analytics',
      status: 'error',
      message: `Analytics test failed: ${error.message}`,
      details: error
    };
  }
};

/**
 * Run comprehensive Firebase connection test
 */
export const runFirebaseConnectionTest = async (): Promise<FirebaseTestResult[]> => {
  console.log('🔥 Starting comprehensive Firebase connection test...');
  
  const results: FirebaseTestResult[] = [];
  
  try {
    // Test all Firebase services
    const authResult = await testFirebaseAuth();
    results.push(authResult);
    
    const firestoreResult = await testFirestore();
    results.push(firestoreResult);
    
    const storageResult = await testFirebaseStorage();
    results.push(storageResult);
    
    const analyticsResult = await testFirebaseAnalytics();
    results.push(analyticsResult);
    
    // Summary
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    const warningCount = results.filter(r => r.status === 'warning').length;
    
    console.log(`🔥 Firebase connection test completed:`);
    console.log(`✅ Success: ${successCount}`);
    console.log(`⚠️ Warnings: ${warningCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    
    return results;
  } catch (error: any) {
    console.error('Firebase connection test failed:', error);
    results.push({
      service: 'Overall Test',
      status: 'error',
      message: `Connection test failed: ${error.message}`,
      details: error
    });
    return results;
  }
};

/**
 * Get Firebase project information
 */
export const getFirebaseProjectInfo = () => {
  return {
    projectId: 'one-touch-hotel-booking',
    authDomain: 'one-touch-hotel-booking.firebaseapp.com',
    databaseURL: 'https://one-touch-hotel-booking-default-rtdb.firebaseio.com',
    storageBucket: 'one-touch-hotel-booking.firebasestorage.app',
    region: 'us-central1', // Default region
    services: {
      authentication: '✅ Enabled',
      firestore: '✅ Enabled',
      storage: '✅ Enabled',
      analytics: '✅ Enabled',
      realtimeDatabase: '✅ Enabled'
    }
  };
};
