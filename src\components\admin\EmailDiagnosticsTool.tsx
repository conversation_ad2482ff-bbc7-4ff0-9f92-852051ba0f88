import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Mail, 
  Search, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2,
  RefreshCw,
  Info,
  Shield,
  Clock
} from 'lucide-react';
import { 
  sendPasswordResetEmailService, 
  checkEmailDeliveryStatus,
  EmailDiagnostics 
} from '@/services/passwordResetService';

interface EmailTestResult {
  email: string;
  success: boolean;
  message: string;
  diagnostics?: EmailDiagnostics;
  timestamp: Date;
  error?: string;
}

export const EmailDiagnosticsTool: React.FC = () => {
  const [testEmail, setTestEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<EmailTestResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<EmailTestResult | null>(null);

  const handleTestEmail = async () => {
    if (!testEmail.trim()) return;

    setLoading(true);
    try {
      // Import both methods
      const { sendPasswordResetEmailService, sendPasswordResetEmailDirect } = await import('@/services/passwordResetService');

      console.log('Testing email:', testEmail);

      // Try enhanced method first
      let result = await sendPasswordResetEmailService(testEmail, {
        url: `${window.location.origin}/reset-password`,
        handleCodeInApp: false,
      });

      // If enhanced method fails with user-not-found, try direct method
      if (!result.success && result.error === 'auth/user-not-found') {
        console.log('Enhanced method failed, trying direct method');
        const directResult = await sendPasswordResetEmailDirect(testEmail, {
          url: `${window.location.origin}/reset-password`,
          handleCodeInApp: false,
        });

        // Update result with direct method outcome
        result = {
          ...directResult,
          message: directResult.success
            ? `${directResult.message} (via direct method)`
            : `Enhanced method failed: ${result.message}. Direct method: ${directResult.message}`,
          diagnostics: result.diagnostics
        };
      }

      const testResult: EmailTestResult = {
        email: testEmail,
        success: result.success,
        message: result.message,
        diagnostics: result.diagnostics,
        timestamp: new Date(),
        error: result.error
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 9)]); // Keep last 10 results
      setSelectedResult(testResult);
    } catch (error: any) {
      const testResult: EmailTestResult = {
        email: testEmail,
        success: false,
        message: 'Test failed with error',
        timestamp: new Date(),
        error: error.message
      };
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
      setSelectedResult(testResult);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getProviderBadgeColor = (provider: string) => {
    const colors: { [key: string]: string } = {
      'Gmail': 'bg-red-100 text-red-800',
      'Yahoo': 'bg-purple-100 text-purple-800',
      'Outlook': 'bg-blue-100 text-blue-800',
      'Other': 'bg-gray-100 text-gray-800'
    };
    return colors[provider] || colors['Other'];
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Delivery Diagnostics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="test-email">Test Email Address</Label>
              <Input
                id="test-email"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                onKeyPress={(e) => e.key === 'Enter' && handleTestEmail()}
              />
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleTestEmail} 
                disabled={loading || !testEmail.trim()}
                className="gap-2"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
                Test Email
              </Button>
            </div>
          </div>

          {selectedResult && (
            <Alert className={selectedResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <div className="flex items-center gap-2">
                {getStatusIcon(selectedResult.success)}
                <AlertDescription className="flex-1">
                  <strong>Latest Test Result:</strong> {selectedResult.message}
                </AlertDescription>
              </div>
            </Alert>
          )}
        </CardContent>
      </Card>

      {selectedResult?.diagnostics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Email Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <Label className="text-sm text-gray-500">Provider</Label>
                <Badge className={getProviderBadgeColor(selectedResult.diagnostics.provider)}>
                  {selectedResult.diagnostics.provider}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm text-gray-500">Domain</Label>
                <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                  {selectedResult.diagnostics.domain}
                </code>
              </div>
              <div className="space-y-1">
                <Label className="text-sm text-gray-500">Format</Label>
                <Badge variant={selectedResult.diagnostics.emailFormat === 'valid' ? 'default' : 'destructive'}>
                  {selectedResult.diagnostics.emailFormat}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm text-gray-500">Account Exists</Label>
                <Badge variant={selectedResult.diagnostics.emailExists ? 'default' : 'secondary'}>
                  {selectedResult.diagnostics.emailExists ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>

            {selectedResult.diagnostics.suggestions.length > 0 && (
              <div>
                <Label className="text-sm font-medium">Suggestions</Label>
                <ul className="mt-2 space-y-1">
                  {selectedResult.diagnostics.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {selectedResult.diagnostics.troubleshooting.length > 0 && (
              <div>
                <Label className="text-sm font-medium">Troubleshooting Steps</Label>
                <ul className="mt-2 space-y-1">
                  {selectedResult.diagnostics.troubleshooting.map((step, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      {step}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedResult === result 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedResult(result)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.success)}
                      <span className="font-medium">{result.email}</span>
                      {result.diagnostics && (
                        <Badge className={getProviderBadgeColor(result.diagnostics.provider)}>
                          {result.diagnostics.provider}
                        </Badge>
                      )}
                    </div>
                    <span className="text-sm text-gray-500">
                      {result.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Common Email Delivery Issues
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border-l-4 border-red-500 pl-4">
              <h4 className="font-medium text-red-800">Spam Filtering</h4>
              <p className="text-sm text-red-600">
                Most email delivery issues are caused by spam filters. Emails often end up in spam/junk folders.
              </p>
            </div>
            <div className="border-l-4 border-yellow-500 pl-4">
              <h4 className="font-medium text-yellow-800">Provider Blocking</h4>
              <p className="text-sm text-yellow-600">
                Some email providers block automated emails from Firebase. Gmail generally works best.
              </p>
            </div>
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-medium text-blue-800">Rate Limiting</h4>
              <p className="text-sm text-blue-600">
                Firebase has rate limits for password reset emails. Too many requests can cause temporary blocks.
              </p>
            </div>
            <div className="border-l-4 border-green-500 pl-4">
              <h4 className="font-medium text-green-800">Domain Configuration</h4>
              <p className="text-sm text-green-600">
                Ensure your Firebase project domain is properly configured for email delivery.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
